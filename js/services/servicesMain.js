/**
 * Services Main Module
 * 
 * Main initialization and coordination module for the services redesign.
 * Orchestrates all service components and handles the overall flow.
 */

import ServiceShowcase from '../components/services/showcase/ServiceShowcase.js';
import CategoryNavigation from '../components/services/navigation/CategoryNavigation.js';
import ServiceGallery from '../components/services/gallery/ServiceGallery.js';
import BookingIntegration from '../components/services/booking/BookingIntegration.js';
import serviceDataManager from './utils/serviceData.js';

/**
 * Services Main Class
 * Coordinates all service components and manages the overall services section
 */
class ServicesMain {
  constructor(options = {}) {
    this.options = {
      containerSelector: '.services-section',
      showcaseSelector: '.service-showcase',
      categoryNavSelector: '.services-category-nav',
      autoInit: true,
      enableGallery: true,
      enableBooking: true,
      ...options
    };

    this.container = null;
    this.showcase = null;
    this.categoryNav = null;
    this.gallery = null;
    this.bookingIntegration = null;
    
    this.services = [];
    this.categories = [];
    this.isInitialized = false;

    if (this.options.autoInit) {
      this.init();
    }
  }

  /**
   * Initialize the services section
   */
  async init() {
    try {
      // Find container
      this.container = document.querySelector(this.options.containerSelector);
      if (!this.container) {
   