/**
 * ConversationFlow - Barber Brothers Conversation Logic
 * Contains the BarberBuddy class and VoiceBooking functionality
 */

/**
 * <PERSON><PERSON><PERSON>y - AI Voice Agent for Barber Brothers
 */
class BarberBuddy {
  constructor(voiceAgent) {
    this.voiceAgent = voiceAgent;
    this.userProfile = null;
    this.services = {
      'classic': {
        name: "Classic Haircut",
        price: "$30",
        duration: "30 min",
        desc: "Traditional cut with scissors and clippers"
      },
      'fade': {
        name: "Fade Master",
        price: "$35",
        duration: "45 min",
        desc: "Perfect blend from skin to top"
      },
      'beard': {
        name: "Beard Game Strong",
        price: "$25",
        duration: "20 min",
        desc: "Trim, shape, and line up your beard"
      },
      'combo': {
        name: "The Full Package",
        price: "$50",
        duration: "60 min",
        desc: "Haircut + beard trim + hot towel"
      }
    };
  }

  async start(greeting, clientHistory) {
    this.isActive = true;
    this.context.userProfile = clientHistory;
    this.barberBuddy.userProfile = clientHistory;
    
    // Start the greeting flow
    await this.barberBuddy.greetUser();
  }

  async end() {
    this.isActive = false;
    this.context = {
      currentStep: 'greeting',
      selectedService: null,
      userProfile: null
    };
  }

  async handleResponse(response) {
    // Handle user responses and continue conversation
    if (response.intent === 'service_selection') {
      await this.barberBuddy.handleServiceSelection(response.serviceKey);
    } else if (response.intent === 'booking') {
      await this.voiceBooking.startBooking();
    }
  }

  getContext() {
    return this.context;
  }
}

/**
 * BarberBuddy - AI Voice Agent for Barber Brothers
 */
class BarberBuddy {
  constructor(voiceAgent) {
    this.voiceAgent = voiceAgent;
    this.userProfile = null;
    this.services = {
      'classic': { 
        name: "Classic Haircut", 
        price: "$30", 
        duration: "30 min", 
        desc: "Traditional cut with scissors and clippers" 
      },
      'fade': { 
        name: "Fade Master", 
        price: "$35", 
        duration: "45 min", 
        desc: "Perfect blend from skin to top" 
      },
      'beard': { 
        name: "Beard Game Strong", 
        price: "$25", 
        duration: "20 min", 
        desc: "Trim, shape, and line up your beard" 
      },
      'combo': { 
        name: "The Full Package", 
        price: "$50", 
        duration: "60 min", 
        desc: "Haircut + beard trim + hot towel" 
      }
    };
  }

  async greetUser() {
    if (!this.userProfile || !this.userProfile.lastVisit) {
      await this.newClientWelcome();
    } else {
      await this.returningClientGreeting();
    }
  }

  async newClientWelcome() {
    const welcome = [
      `Hey ${this.userProfile?.name || 'there'}! Welcome to Barber Brothers! 🎉`,
      `I'm your Barber Buddy - I'll help you find the perfect style!`,
      `So here's the deal - we've got four main services. Want me to break 'em down real quick?`
    ];
    
    await this.speak(welcome.join(' '));
    await this.educateServices();
  }

  async educateServices() {
    // Show the service education modal
    this.showServiceEducationModal();
    
    const education = [
      `Alright, here's what we're working with:`,
      `Classic Haircut - $30, 30 mins. Your timeless look, scissors and clippers.`,
      `Fade Master - $35, 45 mins. That clean fade from skin to top - Andre's specialty!`,
      `Beard Game Strong - $25, 20 mins. Gets your beard looking sharp.`,
      `Or go Full Package - $50, 60 mins. Haircut, beard, hot towel treatment. Total refresh!`
    ];
    
    await this.speak(education.join(' '));
    await this.speak("Which one sounds like your vibe?");
  }

  async returningClientGreeting() {
    const daysSince = this.calculateDaysSince(this.userProfile.lastVisit.date);
    const lastService = this.services[this.userProfile.lastVisit.service];
    
    const greeting = [
      `Yo ${this.userProfile.name}! Been ${daysSince} days since your last ${lastService?.name || 'visit'}.`,
      `Looking fresh last time, by the way! 🔥`,
      `Ready for the same thing or want to switch it up?`
    ];
    
    await this.speak(greeting.join(' '));
    
    if (daysSince > 35) {
      await this.speak("Actually, you're right on schedule for a touch-up!");
    }
  }

  async handleServiceSelection(serviceKey) {
    const service = this.services[serviceKey];
    
    if (!this.userProfile?.lastVisit) {
      await this.speak(`Nice choice! The ${service.name} is ${service.desc}. `);
      await this.speak(`Perfect for ${this.suggestBasedOnProfile()}`);
    }
    
    await this.proceedToBooking(serviceKey);
  }

  suggestBasedOnProfile() {
    // Simple personality-based suggestions
    if (this.userProfile?.name && this.userProfile.name.length > 6) return "making a statement";
    return "keeping it clean and professional";
  }

  async proceedToBooking(serviceKey) {
    // This would integrate with the VoiceBooking class
    const voiceBooking = new VoiceBooking(this);
    await voiceBooking.quickBook(serviceKey);
  }

  calculateDaysSince(date) {
    const now = new Date();
    const lastVisit = new Date(date);
    const diffTime = Math.abs(now - lastVisit);
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  showServiceEducationModal() {
    // Create and show the service education modal
    const modal = document.createElement('div');
    modal.className = 'service-education';
    modal.innerHTML = this.generateServiceCardsHTML();
    document.body.appendChild(modal);
    
    // Show the modal
    modal.style.display = 'block';
    
    // Add click handlers for service selection
    modal.querySelectorAll('.service-card').forEach(card => {
      card.addEventListener('click', (e) => {
        const serviceKey = e.currentTarget.dataset.service;
        this.handleServiceSelection(serviceKey);
        modal.remove();
      });
    });
    
    // Auto-hide after speaking
    setTimeout(() => {
      if (modal.parentNode) {
        modal.remove();
      }
    }, 15000);
  }

  generateServiceCardsHTML() {
    return `
      <div class="service-cards-container">
        ${Object.entries(this.services).map(([key, service]) => `
          <div class="service-card" data-service="${key}">
            <div class="service-name">${service.name}</div>
            <div class="service-price">${service.price}</div>
            <div class="service-desc">${service.desc}</div>
          </div>
        `).join('')}
      </div>
    `;
  }

  async speak(text) {
    // Delegate to the main voice agent
    if (this.voiceAgent) {
      await this.voiceAgent.speak(text);
    } else {
      // Fallback for standalone usage
      return new Promise(resolve => {
        if (!window.speechSynthesis) {
          console.log("Voice not supported:", text);
          resolve();
          return;
        }
        
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.rate = 0.95;
        utterance.pitch = 1.1;
        utterance.volume = 0.8;
        
        utterance.onstart = () => {
          window.pulsingBall?.start();
        };
        
        utterance.onend = () => {
          window.pulsingBall?.stop();
          resolve();
        };
        
        speechSynthesis.speak(utterance);
      });
    }
  }
}

/**
 * VoiceBooking - Handles booking flow
 */
class VoiceBooking {
  constructor(barberBuddy) {
    this.barberBuddy = barberBuddy;
    this.currentStep = 'service';
  }

  async startBooking() {
    await this.barberBuddy.speak("Let's get you booked! What's your name?");
    // This would integrate with your existing booking system
  }

  async quickBook(serviceKey) {
    const service = this.barberBuddy.services[serviceKey];
    const quickResponses = [
      `Boom! ${service.name} locked in! 🎯`,
      `You got it! ${service.name} - you're all set!`,
      `Perfect choice! ${service.name} it is!`
    ];
    
    const response = quickResponses[Math.floor(Math.random() * quickResponses.length)];
    await this.barberBuddy.speak(response);
  }
}

export { BarberBuddy, VoiceBooking };
